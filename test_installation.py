#!/usr/bin/env python3
"""
Test script to verify installation and dependencies
"""

import sys
import importlib

def test_imports():
    """Test if all required modules can be imported"""
    required_modules = [
        'requests',
        'beautifulsoup4',
        'selenium',
        'pandas',
        'fake_useragent',
        'webdriver_manager'
    ]
    
    print("Testing module imports...")
    
    for module in required_modules:
        try:
            if module == 'beautifulsoup4':
                importlib.import_module('bs4')
                print(f"✅ {module} (bs4) - OK")
            elif module == 'fake_useragent':
                importlib.import_module('fake_useragent')
                print(f"✅ {module} - OK")
            elif module == 'webdriver_manager':
                importlib.import_module('webdriver_manager')
                print(f"✅ {module} - OK")
            else:
                importlib.import_module(module)
                print(f"✅ {module} - OK")
        except ImportError as e:
            print(f"❌ {module} - FAILED: {e}")
            return False
    
    return True

def test_local_modules():
    """Test if local modules can be imported"""
    local_modules = ['config', 'utils', 'scraper']
    
    print("\nTesting local modules...")
    
    for module in local_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module} - OK")
        except ImportError as e:
            print(f"❌ {module} - FAILED: {e}")
            return False
    
    return True

def test_selenium_setup():
    """Test Selenium WebDriver setup"""
    print("\nTesting Selenium setup...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        
        # Test ChromeDriver download
        print("Downloading ChromeDriver...")
        driver_path = ChromeDriverManager().install()
        print(f"✅ ChromeDriver installed at: {driver_path}")
        
        # Test Chrome options
        options = Options()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        print("✅ Chrome options configured")
        
        return True
        
    except Exception as e:
        print(f"❌ Selenium setup failed: {e}")
        return False

def test_scraper_initialization():
    """Test scraper initialization"""
    print("\nTesting scraper initialization...")
    
    try:
        from scraper import FreeFireScraper
        
        # Test with Selenium
        print("Testing with Selenium...")
        scraper = FreeFireScraper(headless=True, use_selenium=True)
        scraper.close()
        print("✅ Selenium scraper - OK")
        
        # Test without Selenium
        print("Testing without Selenium...")
        scraper = FreeFireScraper(headless=True, use_selenium=False)
        scraper.close()
        print("✅ Requests-only scraper - OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Scraper initialization failed: {e}")
        return False

def test_utility_functions():
    """Test utility functions"""
    print("\nTesting utility functions...")
    
    try:
        from utils import validate_uid, generate_filename, clean_text
        
        # Test UID validation
        assert validate_uid("123456789") == True
        assert validate_uid("12345") == False
        assert validate_uid("abc123") == False
        print("✅ UID validation - OK")
        
        # Test filename generation
        filename = generate_filename("123456789", "json", timestamp=False)
        assert filename == "ff_player_123456789.json"
        print("✅ Filename generation - OK")
        
        # Test text cleaning
        cleaned = clean_text("  Hello   World  \n\t  ")
        assert cleaned == "Hello World"
        print("✅ Text cleaning - OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Utility functions failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Free Fire ID Scraper - Installation Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_local_modules,
        test_selenium_setup,
        test_scraper_initialization,
        test_utility_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Installation is successful.")
        print("\nYou can now use the scraper with:")
        print("python main.py --uid YOUR_UID --region YOUR_REGION")
    else:
        print("❌ Some tests failed. Please check the installation.")
        print("\nTry running: pip install -r requirements.txt")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
