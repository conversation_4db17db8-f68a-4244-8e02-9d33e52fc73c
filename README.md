# Free Fire ID Scraper

A comprehensive Python web scraper for extracting Free Fire player information by UID from hlgamingofficial.com.

## Features

- **Extract comprehensive player data** including rank, level, stats, outfits, guild info, and more
- **Support for multiple regions** (India, Brazil, Singapore, Pakistan, etc.)
- **Batch processing** for multiple UIDs
- **Multiple output formats** (JSON, CSV)
- **Selenium and requests support** for reliable scraping
- **Error handling and retry logic**
- **Command-line interface** for easy usage

## Installation

1. Clone or download this repository
2. Install required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

### Single UID

```bash
# Basic usage
python main.py --uid 123456789 --region India

# With full tool for detailed info
python main.py --uid 123456789 --region Pakistan --audio Hindi --full-tool

# Save as CSV
python main.py --uid 123456789 --output csv

# Run in headless mode
python main.py --uid 123456789 --headless
```

### Batch Processing

Create a text file with UIDs (one per line):

```
123456789
987654321
555666777
```

Then run:

```bash
python main.py --batch uids.txt --region Singapore
```

### Command Line Options

- `--uid`: Single Free Fire player UID
- `--batch`: File containing list of UIDs (one per line)
- `--region`: Player region (default: India)
- `--audio`: Audio language (default: English)
- `--full-tool`: Use full tool for detailed information
- `--output`: Output format - json, csv, or both (default: json)
- `--output-dir`: Output directory (default: output)
- `--headless`: Run browser in headless mode
- `--no-selenium`: Use requests only (no Selenium)
- `--verbose`: Enable verbose logging

### Supported Regions

- India
- Brazil
- Singapore
- Russia
- Indonesia
- Taiwan
- United States
- Vietnam
- Thailand
- Middle East
- Pakistan
- CIS
- Bangladesh

### Supported Audio Languages

- English
- Urdu
- Hindi

## Output Data

The scraper extracts the following information:

### Basic Account Info
- Nickname
- Level and Experience
- Likes count
- Region
- Account creation date

### Ranking Information
- Battle Royale max rank and points
- Clash Squad max rank and points

### Guild Information
- Guild name and level
- Member count and capacity
- Guild owner UID

### Additional Data
- Pet information (ID, level, experience)
- Social profile (signature, language)
- Credit score
- Badges and achievements
- Equipped items and outfits

## File Structure

```
scrspff/
├── main.py           # CLI interface
├── scraper.py        # Main scraper class
├── config.py         # Configuration settings
├── utils.py          # Utility functions
├── requirements.txt  # Dependencies
├── README.md         # Documentation
└── output/          # Output directory (created automatically)
```

## Configuration

Edit `config.py` to modify:

- Base URLs and endpoints
- Request headers and timeouts
- Supported regions and languages
- Output settings

## Error Handling

The scraper includes comprehensive error handling:

- UID validation
- Network timeout handling
- Retry logic with exponential backoff
- Graceful handling of missing data
- Detailed logging

## Logging

Logs are written to both console and `scraper.log` file. Use `--verbose` flag for detailed debugging information.

## Examples

### Example 1: Basic Usage
```bash
python main.py --uid 123456789 --region India
```

### Example 2: Detailed Information
```bash
python main.py --uid 123456789 --region Pakistan --audio Urdu --full-tool --output both
```

### Example 3: Batch Processing
```bash
python main.py --batch player_uids.txt --region Singapore --output csv --verbose
```

## Troubleshooting

1. **Selenium Issues**: Make sure Chrome browser is installed. The script will automatically download ChromeDriver.

2. **Network Errors**: Check your internet connection and try again. The scraper includes retry logic.

3. **Invalid UID**: Ensure the UID is 9-12 digits and belongs to a valid Free Fire account.

4. **Missing Data**: Some players may have private profiles or restricted data visibility.

## Legal Notice

This tool is for educational purposes only. Please respect the website's terms of service and use responsibly. Do not overload the server with excessive requests.

## License

This project is provided as-is for educational purposes. Use at your own risk.
