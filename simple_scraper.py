#!/usr/bin/env python3
"""
Simple Free Fire ID Scraper - Direct approach for testing
"""

import requests
import time
import json
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_uid_with_selenium(uid="2003919727", region="India"):
    """Test the UID with a simplified Selenium approach"""
    
    logger.info(f"Testing UID: {uid} with region: {region}")
    
    # Setup Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    # Initialize driver
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        # Navigate to the website
        url = "https://www.hlgamingofficial.com/2025/04/free-fire-id-check-tool-get-player-info.html"
        logger.info(f"Navigating to: {url}")
        driver.get(url)
        
        # Wait for page to load
        time.sleep(5)
        
        # Take a screenshot for debugging
        driver.save_screenshot("page_screenshot.png")
        logger.info("Screenshot saved as page_screenshot.png")
        
        # Try to find the UID input field with various selectors
        uid_selectors = [
            "input[name='uid']",
            "input[id='uid']", 
            "input[placeholder*='UID']",
            "input[type='text']",
            "#uid",
            ".uid-input",
            "input"
        ]
        
        uid_input = None
        for selector in uid_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            uid_input = element
                            logger.info(f"Found UID input with selector: {selector}")
                            break
                if uid_input:
                    break
            except Exception as e:
                logger.debug(f"Selector {selector} failed: {e}")
                continue
        
        if not uid_input:
            logger.error("Could not find UID input field")
            # Print page source for debugging
            with open("page_source.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            logger.info("Page source saved to page_source.html for debugging")
            return None
        
        # Clear and enter UID
        logger.info("Entering UID...")
        uid_input.clear()
        uid_input.send_keys(uid)
        
        # Try to find and select region
        try:
            select_elements = driver.find_elements(By.CSS_SELECTOR, "select")
            if select_elements:
                for select_elem in select_elements:
                    try:
                        select = Select(select_elem)
                        options = [opt.text.strip() for opt in select.options]
                        logger.info(f"Found select with options: {options}")
                        
                        if region in options:
                            select.select_by_visible_text(region)
                            logger.info(f"Selected region: {region}")
                            break
                    except Exception as e:
                        logger.debug(f"Failed to handle select: {e}")
        except Exception as e:
            logger.warning(f"Could not find/set region: {e}")
        
        # Try to find and click submit button
        submit_selectors = [
            "button[type='submit']",
            "input[type='submit']",
            "button",
            ".btn",
            ".button",
            "[onclick*='check']",
            "[onclick*='submit']"
        ]
        
        submit_button = None
        for selector in submit_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        text = element.text.lower()
                        if any(word in text for word in ['check', 'submit', 'search', 'get']):
                            submit_button = element
                            logger.info(f"Found submit button: {element.text}")
                            break
                if submit_button:
                    break
            except Exception as e:
                logger.debug(f"Submit selector {selector} failed: {e}")
        
        if submit_button:
            logger.info("Clicking submit button...")
            driver.execute_script("arguments[0].click();", submit_button)
        else:
            logger.warning("Could not find submit button, trying to submit form")
            # Try to submit the form directly
            try:
                uid_input.submit()
            except:
                logger.error("Could not submit form")
        
        # Wait for results
        logger.info("Waiting for results...")
        time.sleep(10)
        
        # Take another screenshot
        driver.save_screenshot("results_screenshot.png")
        logger.info("Results screenshot saved as results_screenshot.png")
        
        # Get page source after submission
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Save the results page source
        with open("results_page.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        logger.info("Results page source saved to results_page.html")
        
        # Try to extract any visible data
        results = extract_player_data(soup, uid)
        
        return results
        
    except Exception as e:
        logger.error(f"Error during scraping: {e}")
        return None
    finally:
        driver.quit()

def extract_player_data(soup, uid):
    """Extract player data from the page"""
    
    logger.info("Extracting player data...")
    
    # Initialize result structure
    result = {
        "uid": uid,
        "nickname": "",
        "level": "",
        "experience": "",
        "likes": "",
        "region": "",
        "status": "unknown",
        "raw_data": {}
    }
    
    # Look for any text that might contain player information
    text_content = soup.get_text()
    
    # Check if there are any results displayed
    if "account details" in text_content.lower():
        result["status"] = "found"
        logger.info("Found account details section")
    elif "not found" in text_content.lower() or "invalid" in text_content.lower():
        result["status"] = "not_found"
        logger.info("Account not found or invalid")
    elif "error" in text_content.lower():
        result["status"] = "error"
        logger.info("Error in processing")
    
    # Try to find specific data patterns
    # Look for common patterns in Free Fire data
    patterns = {
        "nickname": ["nickname", "name", "player"],
        "level": ["level", "lvl"],
        "experience": ["exp", "experience", "xp"],
        "likes": ["likes", "like"],
        "rank": ["rank", "ranking"],
        "guild": ["guild", "clan"]
    }
    
    # Extract any structured data that might be present
    tables = soup.find_all('table')
    divs = soup.find_all('div')
    spans = soup.find_all('span')
    
    # Store raw data for analysis
    result["raw_data"]["tables_count"] = len(tables)
    result["raw_data"]["divs_count"] = len(divs)
    result["raw_data"]["spans_count"] = len(spans)
    
    # Look for any data that might be player information
    for table in tables:
        table_text = table.get_text().strip()
        if table_text:
            result["raw_data"]["table_content"] = table_text[:500]  # First 500 chars
            break
    
    # Check for any JavaScript variables that might contain data
    scripts = soup.find_all('script')
    for script in scripts:
        if script.string:
            script_content = script.string
            if 'player' in script_content.lower() or 'uid' in script_content.lower():
                result["raw_data"]["relevant_script"] = script_content[:200]
                break
    
    logger.info(f"Extraction completed. Status: {result['status']}")
    return result

def main():
    """Main function to test the scraper"""
    
    uid = "2003919727"  # The UID provided by the user
    
    print(f"🔥 Testing Free Fire ID Scraper with UID: {uid}")
    print("=" * 50)
    
    # Test with Selenium
    result = test_uid_with_selenium(uid)
    
    if result:
        print("\n📊 Results:")
        print("-" * 30)
        print(json.dumps(result, indent=2))
        
        # Save results to file
        with open(f"test_results_{uid}.json", "w") as f:
            json.dump(result, f, indent=2)
        print(f"\n💾 Results saved to test_results_{uid}.json")
        
    else:
        print("❌ Failed to get results")
    
    print("\n📁 Generated Files:")
    print("- page_screenshot.png (initial page)")
    print("- results_screenshot.png (after submission)")
    print("- page_source.html (initial page source)")
    print("- results_page.html (results page source)")
    print(f"- test_results_{uid}.json (extracted data)")
    
    print("\n💡 Next Steps:")
    print("1. Check the screenshots to see what the page looks like")
    print("2. Review the HTML files to understand the page structure")
    print("3. Analyze the results to improve the scraper")

if __name__ == "__main__":
    main()
