"""
Utility functions for Free Fire ID scraper
"""

import os
import json
import csv
import time
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
import pandas as pd

def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('scraper.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def create_output_directory(directory: str) -> None:
    """Create output directory if it doesn't exist"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"Created output directory: {directory}")

def save_to_json(data: Dict[Any, Any], filename: str) -> None:
    """Save data to JSON file"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"Data saved to JSON: {filename}")
    except Exception as e:
        print(f"Error saving to JSON: {e}")

def save_to_csv(data: Dict[Any, Any], filename: str) -> None:
    """Save data to CSV file"""
    try:
        # Flatten nested dictionaries for CSV
        flattened_data = flatten_dict(data)
        df = pd.DataFrame([flattened_data])
        df.to_csv(filename, index=False, encoding='utf-8')
        print(f"Data saved to CSV: {filename}")
    except Exception as e:
        print(f"Error saving to CSV: {e}")

def flatten_dict(d: Dict[Any, Any], parent_key: str = '', sep: str = '_') -> Dict[str, Any]:
    """Flatten nested dictionary"""
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        elif isinstance(v, list):
            # Convert lists to comma-separated strings
            items.append((new_key, ', '.join(map(str, v)) if v else ''))
        else:
            items.append((new_key, v))
    return dict(items)

def validate_uid(uid: str) -> bool:
    """Validate Free Fire UID format"""
    if not uid:
        return False
    
    # Remove any whitespace
    uid = uid.strip()
    
    # Check if it's numeric and has appropriate length (typically 9-12 digits)
    if not uid.isdigit():
        return False
    
    if len(uid) < 9 or len(uid) > 12:
        return False
    
    return True

def generate_filename(uid: str, format_type: str, timestamp: bool = True) -> str:
    """Generate filename for output"""
    base_name = f"ff_player_{uid}"
    
    if timestamp:
        timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name += f"_{timestamp_str}"
    
    return f"{base_name}.{format_type}"

def wait_with_backoff(attempt: int, base_delay: float = 1.0) -> None:
    """Wait with exponential backoff"""
    delay = base_delay * (2 ** attempt)
    time.sleep(delay)

def clean_text(text: str) -> str:
    """Clean and normalize text"""
    if not text:
        return ""
    
    # Remove extra whitespace and normalize
    text = ' '.join(text.split())
    
    # Remove special characters that might cause issues
    text = text.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
    
    return text.strip()

def parse_numeric_value(value: str) -> Optional[int]:
    """Parse numeric value from string"""
    if not value:
        return None
    
    # Remove commas and other formatting
    cleaned = ''.join(filter(str.isdigit, str(value)))
    
    try:
        return int(cleaned) if cleaned else None
    except ValueError:
        return None
