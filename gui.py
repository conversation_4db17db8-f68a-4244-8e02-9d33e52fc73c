#!/usr/bin/env python3
"""
Simple GUI for Free Fire ID Scraper using tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import json
import os
from datetime import datetime

from scraper import FreeFireScraper
from config import REGIONS, AUDIO_LANGUAGES
from utils import validate_uid, create_output_directory, save_to_json, save_to_csv

class FreeFireScraperGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Free Fire ID Scraper")
        self.root.geometry("600x700")
        
        self.scraper = None
        self.is_scraping = False
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the user interface"""
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Free Fire ID Scraper", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # UID Input
        ttk.Label(main_frame, text="Player UID:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.uid_var = tk.StringVar()
        self.uid_entry = ttk.Entry(main_frame, textvariable=self.uid_var, width=30)
        self.uid_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # Region Selection
        ttk.Label(main_frame, text="Region:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.region_var = tk.StringVar(value="India")
        region_combo = ttk.Combobox(main_frame, textvariable=self.region_var, 
                                   values=list(REGIONS.keys()), state="readonly")
        region_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # Audio Language
        ttk.Label(main_frame, text="Audio Language:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.audio_var = tk.StringVar(value="English")
        audio_combo = ttk.Combobox(main_frame, textvariable=self.audio_var,
                                  values=list(AUDIO_LANGUAGES.keys()), state="readonly")
        audio_combo.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # Options
        options_frame = ttk.LabelFrame(main_frame, text="Options", padding="5")
        options_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        self.full_tool_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="Use Full Tool (Detailed Info)", 
                       variable=self.full_tool_var).grid(row=0, column=0, sticky=tk.W)
        
        self.headless_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Headless Mode", 
                       variable=self.headless_var).grid(row=1, column=0, sticky=tk.W)
        
        self.selenium_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Use Selenium", 
                       variable=self.selenium_var).grid(row=2, column=0, sticky=tk.W)
        
        # Output Format
        output_frame = ttk.LabelFrame(main_frame, text="Output Format", padding="5")
        output_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        self.output_var = tk.StringVar(value="json")
        ttk.Radiobutton(output_frame, text="JSON", variable=self.output_var, 
                       value="json").grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(output_frame, text="CSV", variable=self.output_var, 
                       value="csv").grid(row=0, column=1, sticky=tk.W)
        ttk.Radiobutton(output_frame, text="Both", variable=self.output_var, 
                       value="both").grid(row=0, column=2, sticky=tk.W)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=2, pady=20)
        
        self.scrape_button = ttk.Button(button_frame, text="Scrape Player Info", 
                                       command=self.start_scraping)
        self.scrape_button.grid(row=0, column=0, padx=5)
        
        ttk.Button(button_frame, text="Batch Process", 
                  command=self.batch_process).grid(row=0, column=1, padx=5)
        
        ttk.Button(button_frame, text="Clear", 
                  command=self.clear_form).grid(row=0, column=2, padx=5)
        
        # Progress Bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        # Results Display
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="5")
        results_frame.grid(row=8, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        
        self.results_text = scrolledtext.ScrolledText(results_frame, height=15, width=70)
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(8, weight=1)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
    
    def start_scraping(self):
        """Start the scraping process"""
        if self.is_scraping:
            messagebox.showwarning("Warning", "Scraping is already in progress!")
            return
        
        uid = self.uid_var.get().strip()
        if not uid:
            messagebox.showerror("Error", "Please enter a UID!")
            return
        
        if not validate_uid(uid):
            messagebox.showerror("Error", "Invalid UID format!")
            return
        
        # Start scraping in a separate thread
        self.is_scraping = True
        self.scrape_button.config(state='disabled')
        self.progress.start()
        
        thread = threading.Thread(target=self.scrape_worker, args=(uid,))
        thread.daemon = True
        thread.start()
    
    def scrape_worker(self, uid):
        """Worker function for scraping"""
        try:
            # Initialize scraper
            self.scraper = FreeFireScraper(
                headless=self.headless_var.get(),
                use_selenium=self.selenium_var.get()
            )
            
            # Get player information
            player_data = self.scraper.get_player_info(
                uid=uid,
                region=self.region_var.get(),
                audio_language=self.audio_var.get(),
                use_full_tool=self.full_tool_var.get()
            )
            
            # Update UI in main thread
            self.root.after(0, self.scraping_completed, player_data, uid)
            
        except Exception as e:
            self.root.after(0, self.scraping_failed, str(e))
        finally:
            if self.scraper:
                self.scraper.close()
    
    def scraping_completed(self, player_data, uid):
        """Handle successful scraping completion"""
        self.is_scraping = False
        self.scrape_button.config(state='normal')
        self.progress.stop()
        
        if player_data:
            # Display results
            self.display_results(player_data)
            
            # Save to file
            self.save_results(player_data, uid)
            
            messagebox.showinfo("Success", "Player information retrieved successfully!")
        else:
            messagebox.showerror("Error", "Failed to retrieve player information!")
    
    def scraping_failed(self, error_message):
        """Handle scraping failure"""
        self.is_scraping = False
        self.scrape_button.config(state='normal')
        self.progress.stop()
        
        messagebox.showerror("Error", f"Scraping failed: {error_message}")
    
    def display_results(self, player_data):
        """Display results in the text widget"""
        self.results_text.delete(1.0, tk.END)
        
        # Format and display the data
        formatted_data = json.dumps(player_data, indent=2, ensure_ascii=False)
        self.results_text.insert(tk.END, formatted_data)
    
    def save_results(self, player_data, uid):
        """Save results to file"""
        try:
            create_output_directory("output")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if self.output_var.get() in ['json', 'both']:
                filename = f"output/ff_player_{uid}_{timestamp}.json"
                save_to_json(player_data, filename)
            
            if self.output_var.get() in ['csv', 'both']:
                filename = f"output/ff_player_{uid}_{timestamp}.csv"
                save_to_csv(player_data, filename)
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save results: {e}")
    
    def batch_process(self):
        """Handle batch processing"""
        file_path = filedialog.askopenfilename(
            title="Select UID file",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if file_path:
            messagebox.showinfo("Batch Processing", 
                              "Batch processing feature will be implemented in a future version.")
    
    def clear_form(self):
        """Clear the form"""
        self.uid_var.set("")
        self.region_var.set("India")
        self.audio_var.set("English")
        self.full_tool_var.set(False)
        self.headless_var.set(True)
        self.selenium_var.set(True)
        self.output_var.set("json")
        self.results_text.delete(1.0, tk.END)

def main():
    """Main function to run the GUI"""
    root = tk.Tk()
    app = FreeFireScraperGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
