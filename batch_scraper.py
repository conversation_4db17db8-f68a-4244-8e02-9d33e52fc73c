#!/usr/bin/env python3
"""
Batch scraper for processing multiple Free Fire UIDs
"""

import os
import time
import json
import csv
from datetime import datetime
from typing import List, Dict, Any

from scraper import FreeFireScraper
from utils import validate_uid, create_output_directory, setup_logging

class BatchScraper:
    """Batch scraper for multiple UIDs"""
    
    def __init__(self, headless: bool = True, use_selenium: bool = True):
        self.logger = setup_logging()
        self.scraper = FreeFireScraper(headless=headless, use_selenium=use_selenium)
        self.results = []
        self.failed_uids = []
    
    def process_uids_from_file(self, file_path: str, region: str = "India", 
                              audio_language: str = "English", 
                              use_full_tool: bool = False,
                              delay: float = 2.0) -> Dict[str, Any]:
        """
        Process UIDs from a text file
        
        Args:
            file_path: Path to file containing UIDs (one per line)
            region: Player region
            audio_language: Audio language preference
            use_full_tool: Whether to use full tool
            delay: Delay between requests in seconds
            
        Returns:
            Dictionary with processing results
        """
        
        if not os.path.exists(file_path):
            self.logger.error(f"File not found: {file_path}")
            return {"success": False, "error": "File not found"}
        
        # Read UIDs from file
        with open(file_path, 'r', encoding='utf-8') as f:
            uids = [line.strip() for line in f if line.strip()]
        
        if not uids:
            self.logger.error("No UIDs found in file")
            return {"success": False, "error": "No UIDs found"}
        
        return self.process_uid_list(uids, region, audio_language, use_full_tool, delay)
    
    def process_uid_list(self, uids: List[str], region: str = "India",
                        audio_language: str = "English",
                        use_full_tool: bool = False,
                        delay: float = 2.0) -> Dict[str, Any]:
        """
        Process a list of UIDs
        
        Args:
            uids: List of UIDs to process
            region: Player region
            audio_language: Audio language preference
            use_full_tool: Whether to use full tool
            delay: Delay between requests in seconds
            
        Returns:
            Dictionary with processing results
        """
        
        self.logger.info(f"Starting batch processing of {len(uids)} UIDs")
        
        start_time = time.time()
        successful = 0
        failed = 0
        
        for i, uid in enumerate(uids, 1):
            self.logger.info(f"Processing UID {i}/{len(uids)}: {uid}")
            
            # Validate UID
            if not validate_uid(uid):
                self.logger.warning(f"Skipping invalid UID: {uid}")
                self.failed_uids.append({"uid": uid, "error": "Invalid format"})
                failed += 1
                continue
            
            # Get player information
            try:
                player_data = self.scraper.get_player_info(
                    uid=uid,
                    region=region,
                    audio_language=audio_language,
                    use_full_tool=use_full_tool
                )
                
                if player_data:
                    self.results.append(player_data)
                    successful += 1
                    self.logger.info(f"✅ Success: {player_data.get('nickname', 'Unknown')}")
                else:
                    self.failed_uids.append({"uid": uid, "error": "No data retrieved"})
                    failed += 1
                    self.logger.error(f"❌ Failed: {uid}")
                
            except Exception as e:
                self.failed_uids.append({"uid": uid, "error": str(e)})
                failed += 1
                self.logger.error(f"❌ Error processing {uid}: {e}")
            
            # Delay between requests
            if i < len(uids):
                time.sleep(delay)
        
        end_time = time.time()
        duration = end_time - start_time
        
        summary = {
            "success": True,
            "total_processed": len(uids),
            "successful": successful,
            "failed": failed,
            "duration_seconds": duration,
            "results": self.results,
            "failed_uids": self.failed_uids
        }
        
        self.logger.info(f"Batch processing completed in {duration:.2f}s")
        self.logger.info(f"Success: {successful}, Failed: {failed}")
        
        return summary
    
    def save_results(self, output_dir: str = "output", format_type: str = "json"):
        """Save batch results to file"""
        
        create_output_directory(output_dir)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format_type in ["json", "both"]:
            # Save successful results
            if self.results:
                filename = os.path.join(output_dir, f"batch_results_{timestamp}.json")
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.results, f, indent=2, ensure_ascii=False)
                self.logger.info(f"Results saved to: {filename}")
            
            # Save failed UIDs
            if self.failed_uids:
                filename = os.path.join(output_dir, f"failed_uids_{timestamp}.json")
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.failed_uids, f, indent=2, ensure_ascii=False)
                self.logger.info(f"Failed UIDs saved to: {filename}")
        
        if format_type in ["csv", "both"]:
            # Save successful results as CSV
            if self.results:
                filename = os.path.join(output_dir, f"batch_results_{timestamp}.csv")
                self._save_csv(self.results, filename)
                self.logger.info(f"CSV results saved to: {filename}")
            
            # Save failed UIDs as CSV
            if self.failed_uids:
                filename = os.path.join(output_dir, f"failed_uids_{timestamp}.csv")
                with open(filename, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=['uid', 'error'])
                    writer.writeheader()
                    writer.writerows(self.failed_uids)
                self.logger.info(f"Failed UIDs CSV saved to: {filename}")
    
    def _save_csv(self, data: List[Dict[str, Any]], filename: str):
        """Save data to CSV file"""
        if not data:
            return
        
        # Flatten nested dictionaries
        flattened_data = []
        for item in data:
            flattened = self._flatten_dict(item)
            flattened_data.append(flattened)
        
        # Get all unique keys
        all_keys = set()
        for item in flattened_data:
            all_keys.update(item.keys())
        
        # Write CSV
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=sorted(all_keys))
            writer.writeheader()
            writer.writerows(flattened_data)
    
    def _flatten_dict(self, d: Dict[str, Any], parent_key: str = '', sep: str = '_') -> Dict[str, Any]:
        """Flatten nested dictionary"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                items.append((new_key, ', '.join(map(str, v)) if v else ''))
            else:
                items.append((new_key, v))
        return dict(items)
    
    def generate_report(self) -> str:
        """Generate a summary report"""
        total = len(self.results) + len(self.failed_uids)
        success_rate = (len(self.results) / total * 100) if total > 0 else 0
        
        report = f"""
Batch Processing Report
======================
Total UIDs Processed: {total}
Successful: {len(self.results)}
Failed: {len(self.failed_uids)}
Success Rate: {success_rate:.1f}%

Successful Players:
"""
        
        for result in self.results[:10]:  # Show first 10
            nickname = result.get('nickname', 'Unknown')
            level = result.get('level', 'Unknown')
            uid = result.get('uid', 'Unknown')
            report += f"  - {nickname} (Level {level}) - UID: {uid}\n"
        
        if len(self.results) > 10:
            report += f"  ... and {len(self.results) - 10} more\n"
        
        if self.failed_uids:
            report += f"\nFailed UIDs:\n"
            for failed in self.failed_uids[:5]:  # Show first 5
                report += f"  - {failed['uid']}: {failed['error']}\n"
            
            if len(self.failed_uids) > 5:
                report += f"  ... and {len(self.failed_uids) - 5} more\n"
        
        return report
    
    def close(self):
        """Close the scraper"""
        if self.scraper:
            self.scraper.close()

def main():
    """Example usage of batch scraper"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Batch Free Fire ID Scraper")
    parser.add_argument('--file', required=True, help='File containing UIDs')
    parser.add_argument('--region', default='India', help='Player region')
    parser.add_argument('--audio', default='English', help='Audio language')
    parser.add_argument('--full-tool', action='store_true', help='Use full tool')
    parser.add_argument('--delay', type=float, default=2.0, help='Delay between requests')
    parser.add_argument('--output', default='json', choices=['json', 'csv', 'both'])
    
    args = parser.parse_args()
    
    # Initialize batch scraper
    batch_scraper = BatchScraper(headless=True, use_selenium=True)
    
    try:
        # Process UIDs
        results = batch_scraper.process_uids_from_file(
            file_path=args.file,
            region=args.region,
            audio_language=args.audio,
            use_full_tool=args.full_tool,
            delay=args.delay
        )
        
        if results["success"]:
            # Save results
            batch_scraper.save_results(format_type=args.output)
            
            # Print report
            print(batch_scraper.generate_report())
        else:
            print(f"Batch processing failed: {results.get('error', 'Unknown error')}")
    
    finally:
        batch_scraper.close()

if __name__ == "__main__":
    main()
