"""
Free Fire ID Scraper - Main scraper class
"""

import requests
import time
import json
import re
from typing import Dict, Any, Optional, List
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from fake_useragent import UserAgent

from config import *
from utils import setup_logging, validate_uid, wait_with_backoff, clean_text, parse_numeric_value

class FreeFireScraper:
    """Main scraper class for Free Fire player information"""
    
    def __init__(self, headless: bool = True, use_selenium: bool = True):
        self.logger = setup_logging()
        self.session = requests.Session()
        self.session.headers.update(HEADERS)
        self.use_selenium = use_selenium
        self.driver = None
        
        if use_selenium:
            self._setup_selenium(headless)
    
    def _setup_selenium(self, headless: bool = True):
        """Setup Selenium WebDriver"""
        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument("--headless")
            
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument(f"--user-agent={UserAgent().random}")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.logger.info("Selenium WebDriver initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Selenium: {e}")
            self.use_selenium = False
    
    def get_player_info(self, uid: str, region: str = "India", audio_language: str = "English", 
                       use_full_tool: bool = False) -> Optional[Dict[str, Any]]:
        """
        Get player information by UID
        
        Args:
            uid: Free Fire player UID
            region: Player region
            audio_language: Audio language preference
            use_full_tool: Whether to use the full tool for detailed info
            
        Returns:
            Dictionary containing player information or None if failed
        """
        
        if not validate_uid(uid):
            self.logger.error(f"Invalid UID format: {uid}")
            return None
        
        if region not in REGIONS:
            self.logger.error(f"Invalid region: {region}")
            return None
        
        self.logger.info(f"Fetching player info for UID: {uid}, Region: {region}")
        
        try:
            if self.use_selenium:
                return self._scrape_with_selenium(uid, region, audio_language, use_full_tool)
            else:
                return self._scrape_with_requests(uid, region, audio_language)
                
        except Exception as e:
            self.logger.error(f"Error fetching player info: {e}")
            return None
    
    def _scrape_with_selenium(self, uid: str, region: str, audio_language: str, 
                            use_full_tool: bool = False) -> Optional[Dict[str, Any]]:
        """Scrape using Selenium WebDriver"""
        
        if not self.driver:
            self.logger.error("Selenium driver not available")
            return None
        
        try:
            # Choose URL based on tool preference
            url = FULL_TOOL_URL if use_full_tool else ID_CHECK_URL
            self.driver.get(url)
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Find and fill the UID input - updated selectors for the actual website
            uid_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[placeholder*='UID'], input[name*='uid'], input[id*='uid'], input[type='text']"))
            )
            uid_input.clear()
            uid_input.send_keys(uid)

            # Select region if dropdown exists
            try:
                # Look for region select dropdown
                region_selects = self.driver.find_elements(By.CSS_SELECTOR, "select")
                if region_selects:
                    # Try to find the region select (usually the first one)
                    for select_elem in region_selects:
                        try:
                            select = Select(select_elem)
                            # Check if this select has region options
                            options = [opt.text for opt in select.options]
                            if region in options:
                                select.select_by_visible_text(region)
                                break
                        except:
                            continue
            except Exception as e:
                self.logger.warning(f"Region selector not found or failed to select: {e}")

            # Select audio language if available
            try:
                # Look for audio language select (usually the second select)
                selects = self.driver.find_elements(By.CSS_SELECTOR, "select")
                if len(selects) > 1:
                    select = Select(selects[1])  # Second select is usually audio
                    options = [opt.text for opt in select.options]
                    if audio_language in options:
                        select.select_by_visible_text(audio_language)
            except Exception as e:
                self.logger.warning(f"Audio language selector not found: {e}")

            # Click submit button - updated selectors
            submit_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "button, input[type='submit'], input[value*='Check'], .btn, .button"))
            )
            submit_button.click()
            
            # Wait for results to load
            time.sleep(5)
            
            # Parse results
            return self._parse_results_selenium()
            
        except Exception as e:
            self.logger.error(f"Selenium scraping failed: {e}")
            return None
    
    def _scrape_with_requests(self, uid: str, region: str, audio_language: str) -> Optional[Dict[str, Any]]:
        """Scrape using requests library"""
        
        try:
            # First, get the page to find form details
            response = self.session.get(ID_CHECK_URL, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for form and extract necessary data
            form = soup.find('form')
            if not form:
                self.logger.error("No form found on the page")
                return None
            
            # Prepare form data
            form_data = {
                'uid': uid,
                'region': region,
                'audio_language': audio_language
            }
            
            # Submit form
            form_action = form.get('action', '')
            if form_action.startswith('/'):
                form_action = BASE_URL + form_action
            elif not form_action.startswith('http'):
                form_action = ID_CHECK_URL
            
            response = self.session.post(form_action, data=form_data, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            
            # Parse response
            return self._parse_results_requests(response.text)
            
        except Exception as e:
            self.logger.error(f"Requests scraping failed: {e}")
            return None

    def _parse_results_selenium(self) -> Optional[Dict[str, Any]]:
        """Parse results from Selenium page"""
        try:
            # Wait for results container to appear
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".account-details, .player-info, .result-container"))
            )

            # Get page source and parse with BeautifulSoup
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            return self._extract_player_data(soup)

        except Exception as e:
            self.logger.error(f"Failed to parse Selenium results: {e}")
            return None

    def _parse_results_requests(self, html_content: str) -> Optional[Dict[str, Any]]:
        """Parse results from requests response"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            return self._extract_player_data(soup)

        except Exception as e:
            self.logger.error(f"Failed to parse requests results: {e}")
            return None

    def _extract_player_data(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract player data from parsed HTML"""
        player_data = {
            'uid': '',
            'nickname': '',
            'level': None,
            'experience': None,
            'likes': None,
            'region': '',
            'account_creation_date': '',
            'rank_info': {},
            'guild_info': {},
            'pet_info': {},
            'social_info': {},
            'credit_score': None,
            'equipped_items': {},
            'badges': [],
            'timestamp': time.time()
        }

        try:
            # Extract basic account info
            player_data['nickname'] = self._extract_text(soup, [
                '.nickname', '.player-name', '[data-field="nickname"]'
            ])

            player_data['level'] = parse_numeric_value(self._extract_text(soup, [
                '.level', '.player-level', '[data-field="level"]'
            ]))

            player_data['experience'] = parse_numeric_value(self._extract_text(soup, [
                '.experience', '.exp', '[data-field="experience"]'
            ]))

            player_data['likes'] = parse_numeric_value(self._extract_text(soup, [
                '.likes', '.player-likes', '[data-field="likes"]'
            ]))

            # Extract rank information
            player_data['rank_info'] = {
                'br_max_rank': parse_numeric_value(self._extract_text(soup, [
                    '.br-rank', '.battle-royale-rank', '[data-field="br_rank"]'
                ])),
                'cs_max_rank': parse_numeric_value(self._extract_text(soup, [
                    '.cs-rank', '.clash-squad-rank', '[data-field="cs_rank"]'
                ])),
                'br_rank_points': parse_numeric_value(self._extract_text(soup, [
                    '.br-points', '.br-rank-points', '[data-field="br_points"]'
                ])),
                'cs_rank_points': parse_numeric_value(self._extract_text(soup, [
                    '.cs-points', '.cs-rank-points', '[data-field="cs_points"]'
                ]))
            }

            # Extract guild information
            player_data['guild_info'] = {
                'name': self._extract_text(soup, [
                    '.guild-name', '[data-field="guild_name"]'
                ]),
                'level': parse_numeric_value(self._extract_text(soup, [
                    '.guild-level', '[data-field="guild_level"]'
                ])),
                'member_count': parse_numeric_value(self._extract_text(soup, [
                    '.guild-members', '[data-field="guild_members"]'
                ])),
                'capacity': parse_numeric_value(self._extract_text(soup, [
                    '.guild-capacity', '[data-field="guild_capacity"]'
                ])),
                'owner_uid': self._extract_text(soup, [
                    '.guild-owner', '[data-field="guild_owner"]'
                ])
            }

            # Extract additional data if available
            self._extract_additional_data(soup, player_data)

        except Exception as e:
            self.logger.error(f"Error extracting player data: {e}")

        return player_data

    def _extract_text(self, soup: BeautifulSoup, selectors: List[str]) -> str:
        """Extract text using multiple selectors"""
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return clean_text(element.get_text())
        return ""

    def _extract_additional_data(self, soup: BeautifulSoup, player_data: Dict[str, Any]):
        """Extract additional player data"""
        try:
            # Extract pet information
            player_data['pet_info'] = {
                'pet_id': self._extract_text(soup, ['.pet-id', '[data-field="pet_id"]']),
                'pet_level': parse_numeric_value(self._extract_text(soup, [
                    '.pet-level', '[data-field="pet_level"]'
                ])),
                'pet_experience': parse_numeric_value(self._extract_text(soup, [
                    '.pet-exp', '[data-field="pet_exp"]'
                ]))
            }

            # Extract social information
            player_data['social_info'] = {
                'signature': self._extract_text(soup, [
                    '.signature', '.bio', '[data-field="signature"]'
                ]),
                'language': self._extract_text(soup, [
                    '.language', '[data-field="language"]'
                ])
            }

            # Extract credit score
            player_data['credit_score'] = parse_numeric_value(self._extract_text(soup, [
                '.credit-score', '[data-field="credit_score"]'
            ]))

            # Extract badges
            badge_elements = soup.select('.badge, .achievement, [data-field="badge"]')
            player_data['badges'] = [clean_text(badge.get_text()) for badge in badge_elements]

        except Exception as e:
            self.logger.warning(f"Error extracting additional data: {e}")

    def close(self):
        """Close the scraper and cleanup resources"""
        if self.driver:
            self.driver.quit()
            self.logger.info("Selenium driver closed")

        if self.session:
            self.session.close()
            self.logger.info("Requests session closed")
