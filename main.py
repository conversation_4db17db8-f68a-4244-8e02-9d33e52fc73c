#!/usr/bin/env python3
"""
Free Fire ID Scraper - Command Line Interface
"""

import argparse
import sys
import os
from typing import Optional

from scraper import <PERSON><PERSON><PERSON><PERSON><PERSON>raper
from config import REGIONS, AUDIO_LANGUAGES, OUTPUT_DIR, DEFAULT_OUTPUT_FORMAT
from utils import (
    create_output_directory, save_to_json, save_to_csv, 
    generate_filename, validate_uid, setup_logging
)

def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(
        description="Free Fire Player Information Scraper",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --uid 123456789 --region India
  python main.py --uid 123456789 --region Pakistan --audio Hindi --full-tool
  python main.py --uid 123456789 --output json --headless
  python main.py --batch uids.txt --region Singapore
        """
    )
    
    # Required arguments
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--uid', type=str, help='Free Fire player UID')
    group.add_argument('--batch', type=str, help='File containing list of UIDs (one per line)')
    
    # Optional arguments
    parser.add_argument('--region', type=str, default='India', 
                       choices=list(REGIONS.keys()),
                       help='Player region (default: India)')
    
    parser.add_argument('--audio', type=str, default='English',
                       choices=list(AUDIO_LANGUAGES.keys()),
                       help='Audio language (default: English)')
    
    parser.add_argument('--full-tool', action='store_true',
                       help='Use full tool for detailed information')
    
    parser.add_argument('--output', type=str, default=DEFAULT_OUTPUT_FORMAT,
                       choices=['json', 'csv', 'both'],
                       help='Output format (default: json)')
    
    parser.add_argument('--output-dir', type=str, default=OUTPUT_DIR,
                       help=f'Output directory (default: {OUTPUT_DIR})')
    
    parser.add_argument('--headless', action='store_true',
                       help='Run browser in headless mode')
    
    parser.add_argument('--no-selenium', action='store_true',
                       help='Use requests only (no Selenium)')
    
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = "DEBUG" if args.verbose else "INFO"
    logger = setup_logging(log_level)
    
    # Create output directory
    create_output_directory(args.output_dir)
    
    # Initialize scraper
    use_selenium = not args.no_selenium
    scraper = FreeFireScraper(headless=args.headless, use_selenium=use_selenium)
    
    try:
        if args.uid:
            # Single UID processing
            process_single_uid(scraper, args)
        else:
            # Batch processing
            process_batch_uids(scraper, args)
            
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        scraper.close()

def process_single_uid(scraper: FreeFireScraper, args) -> None:
    """Process a single UID"""
    logger = setup_logging()
    
    if not validate_uid(args.uid):
        logger.error(f"Invalid UID format: {args.uid}")
        sys.exit(1)
    
    logger.info(f"Processing UID: {args.uid}")
    
    # Get player information
    player_data = scraper.get_player_info(
        uid=args.uid,
        region=args.region,
        audio_language=args.audio,
        use_full_tool=args.full_tool
    )
    
    if not player_data:
        logger.error("Failed to retrieve player information")
        sys.exit(1)
    
    # Save results
    save_results(player_data, args.uid, args.output_dir, args.output)
    logger.info("Processing completed successfully")

def process_batch_uids(scraper: FreeFireScraper, args) -> None:
    """Process multiple UIDs from file"""
    logger = setup_logging()
    
    if not os.path.exists(args.batch):
        logger.error(f"Batch file not found: {args.batch}")
        sys.exit(1)
    
    # Read UIDs from file
    with open(args.batch, 'r') as f:
        uids = [line.strip() for line in f if line.strip()]
    
    if not uids:
        logger.error("No UIDs found in batch file")
        sys.exit(1)
    
    logger.info(f"Processing {len(uids)} UIDs from batch file")
    
    successful = 0
    failed = 0
    
    for i, uid in enumerate(uids, 1):
        logger.info(f"Processing UID {i}/{len(uids)}: {uid}")
        
        if not validate_uid(uid):
            logger.warning(f"Skipping invalid UID: {uid}")
            failed += 1
            continue
        
        # Get player information
        player_data = scraper.get_player_info(
            uid=uid,
            region=args.region,
            audio_language=args.audio,
            use_full_tool=args.full_tool
        )
        
        if player_data:
            save_results(player_data, uid, args.output_dir, args.output)
            successful += 1
            logger.info(f"Successfully processed UID: {uid}")
        else:
            failed += 1
            logger.error(f"Failed to process UID: {uid}")
        
        # Small delay between requests
        if i < len(uids):
            import time
            time.sleep(1)
    
    logger.info(f"Batch processing completed. Success: {successful}, Failed: {failed}")

def save_results(player_data: dict, uid: str, output_dir: str, output_format: str) -> None:
    """Save player data to file(s)"""
    
    if output_format in ['json', 'both']:
        filename = os.path.join(output_dir, generate_filename(uid, 'json'))
        save_to_json(player_data, filename)
    
    if output_format in ['csv', 'both']:
        filename = os.path.join(output_dir, generate_filename(uid, 'csv'))
        save_to_csv(player_data, filename)

if __name__ == "__main__":
    main()
