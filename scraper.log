2025-06-05 13:02:37,893 - WDM - INFO - ====== WebDriver manager ======
2025-06-05 13:02:39,532 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:02:39,694 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:02:39,795 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68\chromedriver-win32/chromedriver.exe] found in cache
2025-06-05 13:02:42,758 - utils - INFO - Selenium WebDriver initialized successfully
2025-06-05 13:02:45,010 - utils - INFO - Selenium driver closed
2025-06-05 13:02:45,011 - utils - INFO - Requests session closed
2025-06-05 13:02:45,013 - utils - INFO - Requests session closed
2025-06-05 13:02:58,194 - WDM - INFO - ====== WebDriver manager ======
2025-06-05 13:02:59,687 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:02:59,877 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:03:00,028 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68\chromedriver-win32/chromedriver.exe] found in cache
2025-06-05 13:03:01,262 - utils - INFO - Selenium WebDriver initialized successfully
2025-06-05 13:03:01,263 - utils - INFO - Fetching player info for UID: 123456789, Region: India
2025-06-05 13:03:10,937 - utils - WARNING - Audio language selector not found
2025-06-05 13:03:11,256 - utils - ERROR - Selenium scraping failed: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=137.0.7151.56); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception
Stacktrace:
	GetHandleVerifier [0x0xeb3763+63299]
	GetHandleVerifier [0x0xeb37a4+63364]
	(No symbol) [0x0xce1113]
	(No symbol) [0x0xce7920]
	(No symbol) [0x0xce9c4a]
	(No symbol) [0x0xce9cc7]
	(No symbol) [0x0xd29114]
	(No symbol) [0x0xd29c1b]
	(No symbol) [0x0xd72212]
	(No symbol) [0x0xd4e5c4]
	(No symbol) [0x0xd6fa4a]
	(No symbol) [0x0xd4e376]
	(No symbol) [0x0xd1d6e0]
	(No symbol) [0x0xd1e544]
	GetHandleVerifier [0x0x110e033+2531347]
	GetHandleVerifier [0x0x1109332+2511634]
	GetHandleVerifier [0x0xed9eda+220858]
	GetHandleVerifier [0x0xeca528+156936]
	GetHandleVerifier [0x0xed0c5d+183357]
	GetHandleVerifier [0x0xebb6c8+95912]
	GetHandleVerifier [0x0xebb870+96336]
	GetHandleVerifier [0x0xea664a+9770]
	BaseThreadInitThunk [0x0x75dd5d49+25]
	RtlInitializeExceptionChain [0x0x76f7d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x76f7cfc1+561]

2025-06-05 13:03:13,693 - utils - INFO - Selenium driver closed
2025-06-05 13:03:13,694 - utils - INFO - Requests session closed
2025-06-05 13:03:13,788 - WDM - INFO - ====== WebDriver manager ======
2025-06-05 13:03:15,310 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:03:15,427 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:03:15,554 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68\chromedriver-win32/chromedriver.exe] found in cache
2025-06-05 13:03:17,016 - utils - INFO - Selenium WebDriver initialized successfully
2025-06-05 13:03:17,018 - utils - INFO - Fetching player info for UID: 123456789, Region: India
2025-06-05 13:03:32,572 - utils - WARNING - Audio language selector not found
2025-06-05 13:03:32,831 - utils - ERROR - Selenium scraping failed: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=137.0.7151.56); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception
Stacktrace:
	GetHandleVerifier [0x0xeb3763+63299]
	GetHandleVerifier [0x0xeb37a4+63364]
	(No symbol) [0x0xce1113]
	(No symbol) [0x0xce7920]
	(No symbol) [0x0xce9c4a]
	(No symbol) [0x0xce9cc7]
	(No symbol) [0x0xd29114]
	(No symbol) [0x0xd29c1b]
	(No symbol) [0x0xd72212]
	(No symbol) [0x0xd4e5c4]
	(No symbol) [0x0xd6fa4a]
	(No symbol) [0x0xd4e376]
	(No symbol) [0x0xd1d6e0]
	(No symbol) [0x0xd1e544]
	GetHandleVerifier [0x0x110e033+2531347]
	GetHandleVerifier [0x0x1109332+2511634]
	GetHandleVerifier [0x0xed9eda+220858]
	GetHandleVerifier [0x0xeca528+156936]
	GetHandleVerifier [0x0xed0c5d+183357]
	GetHandleVerifier [0x0xebb6c8+95912]
	GetHandleVerifier [0x0xebb870+96336]
	GetHandleVerifier [0x0xea664a+9770]
	BaseThreadInitThunk [0x0x75dd5d49+25]
	RtlInitializeExceptionChain [0x0x76f7d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x76f7cfc1+561]

2025-06-05 13:03:32,835 - utils - INFO - Fetching player info for UID: 987654321, Region: India
2025-06-05 13:03:49,345 - utils - WARNING - Audio language selector not found
2025-06-05 13:03:49,374 - utils - ERROR - Selenium scraping failed: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=137.0.7151.56); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception
Stacktrace:
	GetHandleVerifier [0x0xeb3763+63299]
	GetHandleVerifier [0x0xeb37a4+63364]
	(No symbol) [0x0xce1113]
	(No symbol) [0x0xce7920]
	(No symbol) [0x0xce9c4a]
	(No symbol) [0x0xce9cc7]
	(No symbol) [0x0xd29114]
	(No symbol) [0x0xd29c1b]
	(No symbol) [0x0xd72212]
	(No symbol) [0x0xd4e5c4]
	(No symbol) [0x0xd6fa4a]
	(No symbol) [0x0xd4e376]
	(No symbol) [0x0xd1d6e0]
	(No symbol) [0x0xd1e544]
	GetHandleVerifier [0x0x110e033+2531347]
	GetHandleVerifier [0x0x1109332+2511634]
	GetHandleVerifier [0x0xed9eda+220858]
	GetHandleVerifier [0x0xeca528+156936]
	GetHandleVerifier [0x0xed0c5d+183357]
	GetHandleVerifier [0x0xebb6c8+95912]
	GetHandleVerifier [0x0xebb870+96336]
	GetHandleVerifier [0x0xea664a+9770]
	BaseThreadInitThunk [0x0x75dd5d49+25]
	RtlInitializeExceptionChain [0x0x76f7d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x76f7cfc1+561]

2025-06-05 13:03:49,378 - utils - INFO - Fetching player info for UID: 555666777, Region: India
2025-06-05 13:03:56,954 - utils - WARNING - Audio language selector not found
2025-06-05 13:03:57,153 - utils - ERROR - Selenium scraping failed: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=137.0.7151.56); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception
Stacktrace:
	GetHandleVerifier [0x0xeb3763+63299]
	GetHandleVerifier [0x0xeb37a4+63364]
	(No symbol) [0x0xce1113]
	(No symbol) [0x0xce7920]
	(No symbol) [0x0xce9c4a]
	(No symbol) [0x0xce9cc7]
	(No symbol) [0x0xd29114]
	(No symbol) [0x0xd29c1b]
	(No symbol) [0x0xd72212]
	(No symbol) [0x0xd4e5c4]
	(No symbol) [0x0xd6fa4a]
	(No symbol) [0x0xd4e376]
	(No symbol) [0x0xd1d6e0]
	(No symbol) [0x0xd1e544]
	GetHandleVerifier [0x0x110e033+2531347]
	GetHandleVerifier [0x0x1109332+2511634]
	GetHandleVerifier [0x0xed9eda+220858]
	GetHandleVerifier [0x0xeca528+156936]
	GetHandleVerifier [0x0xed0c5d+183357]
	GetHandleVerifier [0x0xebb6c8+95912]
	GetHandleVerifier [0x0xebb870+96336]
	GetHandleVerifier [0x0xea664a+9770]
	BaseThreadInitThunk [0x0x75dd5d49+25]
	RtlInitializeExceptionChain [0x0x76f7d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x76f7cfc1+561]

2025-06-05 13:03:59,594 - utils - INFO - Selenium driver closed
2025-06-05 13:03:59,594 - utils - INFO - Requests session closed
2025-06-05 13:03:59,677 - WDM - INFO - ====== WebDriver manager ======
2025-06-05 13:04:01,152 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:04:01,258 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:04:01,431 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68\chromedriver-win32/chromedriver.exe] found in cache
2025-06-05 13:04:02,847 - utils - INFO - Selenium WebDriver initialized successfully
2025-06-05 13:04:02,848 - utils - INFO - Fetching player info for UID: 123456789, Region: India
2025-06-05 13:04:10,175 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001900604A8B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/30ce6237c1246839e856563d7e80d723
2025-06-05 13:04:14,184 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001900604B230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/30ce6237c1246839e856563d7e80d723
2025-06-05 13:04:18,201 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000019006150170>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/30ce6237c1246839e856563d7e80d723
2025-06-05 13:04:22,218 - utils - INFO - Selenium driver closed
2025-06-05 13:04:22,219 - utils - INFO - Requests session closed
