2025-06-05 13:02:37,893 - WDM - INFO - ====== WebDriver manager ======
2025-06-05 13:02:39,532 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:02:39,694 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:02:39,795 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68\chromedriver-win32/chromedriver.exe] found in cache
2025-06-05 13:02:42,758 - utils - INFO - Selenium WebDriver initialized successfully
2025-06-05 13:02:45,010 - utils - INFO - Selenium driver closed
2025-06-05 13:02:45,011 - utils - INFO - Requests session closed
2025-06-05 13:02:45,013 - utils - INFO - Requests session closed
2025-06-05 13:02:58,194 - WDM - INFO - ====== WebDriver manager ======
2025-06-05 13:02:59,687 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:02:59,877 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:03:00,028 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68\chromedriver-win32/chromedriver.exe] found in cache
2025-06-05 13:03:01,262 - utils - INFO - Selenium WebDriver initialized successfully
2025-06-05 13:03:01,263 - utils - INFO - Fetching player info for UID: 123456789, Region: India
2025-06-05 13:03:10,937 - utils - WARNING - Audio language selector not found
2025-06-05 13:03:11,256 - utils - ERROR - Selenium scraping failed: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=137.0.7151.56); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception
Stacktrace:
	GetHandleVerifier [0x0xeb3763+63299]
	GetHandleVerifier [0x0xeb37a4+63364]
	(No symbol) [0x0xce1113]
	(No symbol) [0x0xce7920]
	(No symbol) [0x0xce9c4a]
	(No symbol) [0x0xce9cc7]
	(No symbol) [0x0xd29114]
	(No symbol) [0x0xd29c1b]
	(No symbol) [0x0xd72212]
	(No symbol) [0x0xd4e5c4]
	(No symbol) [0x0xd6fa4a]
	(No symbol) [0x0xd4e376]
	(No symbol) [0x0xd1d6e0]
	(No symbol) [0x0xd1e544]
	GetHandleVerifier [0x0x110e033+2531347]
	GetHandleVerifier [0x0x1109332+2511634]
	GetHandleVerifier [0x0xed9eda+220858]
	GetHandleVerifier [0x0xeca528+156936]
	GetHandleVerifier [0x0xed0c5d+183357]
	GetHandleVerifier [0x0xebb6c8+95912]
	GetHandleVerifier [0x0xebb870+96336]
	GetHandleVerifier [0x0xea664a+9770]
	BaseThreadInitThunk [0x0x75dd5d49+25]
	RtlInitializeExceptionChain [0x0x76f7d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x76f7cfc1+561]

2025-06-05 13:03:13,693 - utils - INFO - Selenium driver closed
2025-06-05 13:03:13,694 - utils - INFO - Requests session closed
2025-06-05 13:03:13,788 - WDM - INFO - ====== WebDriver manager ======
2025-06-05 13:03:15,310 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:03:15,427 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:03:15,554 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68\chromedriver-win32/chromedriver.exe] found in cache
2025-06-05 13:03:17,016 - utils - INFO - Selenium WebDriver initialized successfully
2025-06-05 13:03:17,018 - utils - INFO - Fetching player info for UID: 123456789, Region: India
2025-06-05 13:03:32,572 - utils - WARNING - Audio language selector not found
2025-06-05 13:03:32,831 - utils - ERROR - Selenium scraping failed: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=137.0.7151.56); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception
Stacktrace:
	GetHandleVerifier [0x0xeb3763+63299]
	GetHandleVerifier [0x0xeb37a4+63364]
	(No symbol) [0x0xce1113]
	(No symbol) [0x0xce7920]
	(No symbol) [0x0xce9c4a]
	(No symbol) [0x0xce9cc7]
	(No symbol) [0x0xd29114]
	(No symbol) [0x0xd29c1b]
	(No symbol) [0x0xd72212]
	(No symbol) [0x0xd4e5c4]
	(No symbol) [0x0xd6fa4a]
	(No symbol) [0x0xd4e376]
	(No symbol) [0x0xd1d6e0]
	(No symbol) [0x0xd1e544]
	GetHandleVerifier [0x0x110e033+2531347]
	GetHandleVerifier [0x0x1109332+2511634]
	GetHandleVerifier [0x0xed9eda+220858]
	GetHandleVerifier [0x0xeca528+156936]
	GetHandleVerifier [0x0xed0c5d+183357]
	GetHandleVerifier [0x0xebb6c8+95912]
	GetHandleVerifier [0x0xebb870+96336]
	GetHandleVerifier [0x0xea664a+9770]
	BaseThreadInitThunk [0x0x75dd5d49+25]
	RtlInitializeExceptionChain [0x0x76f7d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x76f7cfc1+561]

2025-06-05 13:03:32,835 - utils - INFO - Fetching player info for UID: 987654321, Region: India
2025-06-05 13:03:49,345 - utils - WARNING - Audio language selector not found
2025-06-05 13:03:49,374 - utils - ERROR - Selenium scraping failed: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=137.0.7151.56); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception
Stacktrace:
	GetHandleVerifier [0x0xeb3763+63299]
	GetHandleVerifier [0x0xeb37a4+63364]
	(No symbol) [0x0xce1113]
	(No symbol) [0x0xce7920]
	(No symbol) [0x0xce9c4a]
	(No symbol) [0x0xce9cc7]
	(No symbol) [0x0xd29114]
	(No symbol) [0x0xd29c1b]
	(No symbol) [0x0xd72212]
	(No symbol) [0x0xd4e5c4]
	(No symbol) [0x0xd6fa4a]
	(No symbol) [0x0xd4e376]
	(No symbol) [0x0xd1d6e0]
	(No symbol) [0x0xd1e544]
	GetHandleVerifier [0x0x110e033+2531347]
	GetHandleVerifier [0x0x1109332+2511634]
	GetHandleVerifier [0x0xed9eda+220858]
	GetHandleVerifier [0x0xeca528+156936]
	GetHandleVerifier [0x0xed0c5d+183357]
	GetHandleVerifier [0x0xebb6c8+95912]
	GetHandleVerifier [0x0xebb870+96336]
	GetHandleVerifier [0x0xea664a+9770]
	BaseThreadInitThunk [0x0x75dd5d49+25]
	RtlInitializeExceptionChain [0x0x76f7d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x76f7cfc1+561]

2025-06-05 13:03:49,378 - utils - INFO - Fetching player info for UID: 555666777, Region: India
2025-06-05 13:03:56,954 - utils - WARNING - Audio language selector not found
2025-06-05 13:03:57,153 - utils - ERROR - Selenium scraping failed: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=137.0.7151.56); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception
Stacktrace:
	GetHandleVerifier [0x0xeb3763+63299]
	GetHandleVerifier [0x0xeb37a4+63364]
	(No symbol) [0x0xce1113]
	(No symbol) [0x0xce7920]
	(No symbol) [0x0xce9c4a]
	(No symbol) [0x0xce9cc7]
	(No symbol) [0x0xd29114]
	(No symbol) [0x0xd29c1b]
	(No symbol) [0x0xd72212]
	(No symbol) [0x0xd4e5c4]
	(No symbol) [0x0xd6fa4a]
	(No symbol) [0x0xd4e376]
	(No symbol) [0x0xd1d6e0]
	(No symbol) [0x0xd1e544]
	GetHandleVerifier [0x0x110e033+2531347]
	GetHandleVerifier [0x0x1109332+2511634]
	GetHandleVerifier [0x0xed9eda+220858]
	GetHandleVerifier [0x0xeca528+156936]
	GetHandleVerifier [0x0xed0c5d+183357]
	GetHandleVerifier [0x0xebb6c8+95912]
	GetHandleVerifier [0x0xebb870+96336]
	GetHandleVerifier [0x0xea664a+9770]
	BaseThreadInitThunk [0x0x75dd5d49+25]
	RtlInitializeExceptionChain [0x0x76f7d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x76f7cfc1+561]

2025-06-05 13:03:59,594 - utils - INFO - Selenium driver closed
2025-06-05 13:03:59,594 - utils - INFO - Requests session closed
2025-06-05 13:03:59,677 - WDM - INFO - ====== WebDriver manager ======
2025-06-05 13:04:01,152 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:04:01,258 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:04:01,431 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68\chromedriver-win32/chromedriver.exe] found in cache
2025-06-05 13:04:02,847 - utils - INFO - Selenium WebDriver initialized successfully
2025-06-05 13:04:02,848 - utils - INFO - Fetching player info for UID: 123456789, Region: India
2025-06-05 13:04:10,175 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001900604A8B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/30ce6237c1246839e856563d7e80d723
2025-06-05 13:04:14,184 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001900604B230>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/30ce6237c1246839e856563d7e80d723
2025-06-05 13:04:18,201 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000019006150170>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/30ce6237c1246839e856563d7e80d723
2025-06-05 13:04:22,218 - utils - INFO - Selenium driver closed
2025-06-05 13:04:22,219 - utils - INFO - Requests session closed
2025-06-05 13:14:15,196 - WDM - INFO - ====== WebDriver manager ======
2025-06-05 13:14:16,793 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:14:16,798 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): googlechromelabs.github.io:443
2025-06-05 13:14:16,963 - urllib3.connectionpool - DEBUG - https://googlechromelabs.github.io:443 "GET /chrome-for-testing/latest-patch-versions-per-build.json HTTP/1.1" 200 11409
2025-06-05 13:14:16,968 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-05 13:14:16,971 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): googlechromelabs.github.io:443
2025-06-05 13:14:17,077 - urllib3.connectionpool - DEBUG - https://googlechromelabs.github.io:443 "GET /chrome-for-testing/latest-patch-versions-per-build.json HTTP/1.1" 200 11409
2025-06-05 13:14:17,084 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68\chromedriver-win32/chromedriver.exe] found in cache
2025-06-05 13:14:17,085 - selenium.webdriver.common.driver_finder - DEBUG - Skipping Selenium Manager; path to chrome driver specified in Service class: C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68\chromedriver-win32/chromedriver.exe
2025-06-05 13:14:17,774 - selenium.webdriver.common.service - DEBUG - Started executable: `C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.68\chromedriver-win32/chromedriver.exe` in a child process with pid: 31096 using 0 to output -3
2025-06-05 13:14:18,280 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:58783/session {'capabilities': {'firstMatch': [{}], 'alwaysMatch': {'browserName': 'chrome', 'pageLoadStrategy': <PageLoadStrategy.normal: 'normal'>, 'goog:chromeOptions': {'extensions': [], 'args': ['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--window-size=1920,1080', '--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Mobile/15E148 Safari/604.1']}}}}
2025-06-05 13:14:18,281 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): localhost:58783
2025-06-05 13:14:19,854 - urllib3.connectionpool - DEBUG - http://localhost:58783 "POST /session HTTP/1.1" 200 0
2025-06-05 13:14:19,856 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":{"capabilities":{"acceptInsecureCerts":false,"browserName":"chrome","browserVersion":"137.0.7151.56","chrome":{"chromedriverVersion":"137.0.7151.68 (2989ffee9373ea8b8623bd98b3cb350a8e95cadc-refs/branch-heads/7151@{#1873})","userDataDir":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\scoped_dir31096_691188960"},"fedcm:accounts":true,"goog:chromeOptions":{"debuggerAddress":"localhost:58786"},"networkConnectionEnabled":false,"pageLoadStrategy":"normal","platformName":"windows","proxy":{},"setWindowRect":true,"strictFileInteractability":false,"timeouts":{"implicit":0,"pageLoad":300000,"script":30000},"unhandledPromptBehavior":"dismiss and notify","webauthn:extension:credBlob":true,"webauthn:extension:largeBlob":true,"webauthn:extension:minPinLength":true,"webauthn:extension:prf":true,"webauthn:virtualAuthenticators":true},"sessionId":"bc02d037c0f9a816b41928d44762b984"}} | headers=HTTPHeaderDict({'Content-Length': '881', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-05 13:14:19,856 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-06-05 13:14:19,857 - utils - INFO - Selenium WebDriver initialized successfully
2025-06-05 13:14:19,858 - utils - INFO - Processing UID: **********
2025-06-05 13:14:19,860 - utils - INFO - Fetching player info for UID: **********, Region: India
2025-06-05 13:14:19,862 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:58783/session/bc02d037c0f9a816b41928d44762b984/url {'url': 'https://www.hlgamingofficial.com/2025/04/free-fire-id-check-tool-get-player-info.html'}
2025-06-05 13:14:34,392 - urllib3.connectionpool - DEBUG - http://localhost:58783 "POST /session/bc02d037c0f9a816b41928d44762b984/url HTTP/1.1" 200 0
2025-06-05 13:14:34,393 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-05 13:14:34,394 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-06-05 13:14:34,395 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:58783/session/bc02d037c0f9a816b41928d44762b984/element {'using': 'tag name', 'value': 'body'}
2025-06-05 13:14:34,493 - urllib3.connectionpool - DEBUG - http://localhost:58783 "POST /session/bc02d037c0f9a816b41928d44762b984/element HTTP/1.1" 200 0
2025-06-05 13:14:34,502 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":{"element-6066-11e4-a52e-4f735466cecf":"f.85D43CD538ACB22872F2F50EA426C3F2.d.B80535B7B3F2EFD1AFB367C16124B40D.e.121"}} | headers=HTTPHeaderDict({'Content-Length': '127', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-05 13:14:34,507 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-06-05 13:14:34,509 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:58783/session/bc02d037c0f9a816b41928d44762b984/element {'using': 'css selector', 'value': "input[placeholder*='UID'], input[name*='uid'], input[id*='uid'], input[type='text']"}
2025-06-05 13:14:34,657 - urllib3.connectionpool - DEBUG - http://localhost:58783 "POST /session/bc02d037c0f9a816b41928d44762b984/element HTTP/1.1" 200 0
2025-06-05 13:14:34,659 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":{"element-6066-11e4-a52e-4f735466cecf":"f.85D43CD538ACB22872F2F50EA426C3F2.d.B80535B7B3F2EFD1AFB367C16124B40D.e.62"}} | headers=HTTPHeaderDict({'Content-Length': '126', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-05 13:14:34,666 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-06-05 13:14:34,670 - selenium.webdriver.remote.remote_connection - DEBUG - POST http://localhost:58783/session/bc02d037c0f9a816b41928d44762b984/element/f.85D43CD538ACB22872F2F50EA426C3F2.d.B80535B7B3F2EFD1AFB367C16124B40D.e.62/clear {}
2025-06-05 13:14:34,944 - urllib3.connectionpool - DEBUG - http://localhost:58783 "POST /session/bc02d037c0f9a816b41928d44762b984/element/f.85D43CD538ACB22872F2F50EA426C3F2.d.B80535B7B3F2EFD1AFB367C16124B40D.e.62/clear HTTP/1.1" 400 0
2025-06-05 13:14:34,948 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=400 | data={"value":{"error":"element not interactable","message":"element not interactable\n  (Session info: chrome=137.0.7151.56)","stacktrace":"\tGetHandleVerifier [0x0x1103763+63299]\n\tGetHandleVerifier [0x0x11037a4+63364]\n\t(No symbol) [0x0xf30f70]\n\t(No symbol) [0x0xf71652]\n\t(No symbol) [0x0xf9e57c]\n\t(No symbol) [0x0xf6eed4]\n\t(No symbol) [0x0xf9e7f4]\n\t(No symbol) [0x0xfbfa4a]\n\t(No symbol) [0x0xf9e376]\n\t(No symbol) [0x0xf6d6e0]\n\t(No symbol) [0x0xf6e544]\n\tGetHandleVerifier [0x0x135e033+2531347]\n\tGetHandleVerifier [0x0x1359332+2511634]\n\tGetHandleVerifier [0x0x1129eda+220858]\n\tGetHandleVerifier [0x0x111a528+156936]\n\tGetHandleVerifier [0x0x1120c5d+183357]\n\tGetHandleVerifier [0x0x110b6c8+95912]\n\tGetHandleVerifier [0x0x110b870+96336]\n\tGetHandleVerifier [0x0x10f664a+9770]\n\tBaseThreadInitThunk [0x0x75dd5d49+25]\n\tRtlInitializeExceptionChain [0x0x76f7d03b+107]\n\tRtlGetAppContainerNamedObjectPath [0x0x76f7cfc1+561]\n"}} | headers=HTTPHeaderDict({'Content-Length': '954', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-05 13:14:34,950 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-06-05 13:14:34,951 - utils - ERROR - Selenium scraping failed: Message: element not interactable
  (Session info: chrome=137.0.7151.56)
Stacktrace:
	GetHandleVerifier [0x0x1103763+63299]
	GetHandleVerifier [0x0x11037a4+63364]
	(No symbol) [0x0xf30f70]
	(No symbol) [0x0xf71652]
	(No symbol) [0x0xf9e57c]
	(No symbol) [0x0xf6eed4]
	(No symbol) [0x0xf9e7f4]
	(No symbol) [0x0xfbfa4a]
	(No symbol) [0x0xf9e376]
	(No symbol) [0x0xf6d6e0]
	(No symbol) [0x0xf6e544]
	GetHandleVerifier [0x0x135e033+2531347]
	GetHandleVerifier [0x0x1359332+2511634]
	GetHandleVerifier [0x0x1129eda+220858]
	GetHandleVerifier [0x0x111a528+156936]
	GetHandleVerifier [0x0x1120c5d+183357]
	GetHandleVerifier [0x0x110b6c8+95912]
	GetHandleVerifier [0x0x110b870+96336]
	GetHandleVerifier [0x0x10f664a+9770]
	BaseThreadInitThunk [0x0x75dd5d49+25]
	RtlInitializeExceptionChain [0x0x76f7d03b+107]
	RtlGetAppContainerNamedObjectPath [0x0x76f7cfc1+561]

2025-06-05 13:14:34,953 - utils - ERROR - Failed to retrieve player information
2025-06-05 13:14:34,955 - selenium.webdriver.remote.remote_connection - DEBUG - DELETE http://localhost:58783/session/bc02d037c0f9a816b41928d44762b984 {}
2025-06-05 13:14:35,959 - urllib3.connectionpool - DEBUG - http://localhost:58783 "DELETE /session/bc02d037c0f9a816b41928d44762b984 HTTP/1.1" 200 0
2025-06-05 13:14:35,961 - selenium.webdriver.remote.remote_connection - DEBUG - Remote response: status=200 | data={"value":null} | headers=HTTPHeaderDict({'Content-Length': '14', 'Content-Type': 'application/json; charset=utf-8', 'cache-control': 'no-cache'})
2025-06-05 13:14:35,961 - selenium.webdriver.remote.remote_connection - DEBUG - Finished Request
2025-06-05 13:14:38,157 - utils - INFO - Selenium driver closed
2025-06-05 13:14:38,157 - utils - INFO - Requests session closed
