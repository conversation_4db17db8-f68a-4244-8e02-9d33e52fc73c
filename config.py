"""
Configuration settings for Free Fire ID scraper
"""

# Base URLs
BASE_URL = "https://www.hlgamingofficial.com"
ID_CHECK_URL = f"{BASE_URL}/2025/04/free-fire-id-check-tool-get-player-info.html"
FULL_TOOL_URL = f"{BASE_URL}/p/search-free-fire-uids.html"

# Supported regions
REGIONS = {
    "India": "India",
    "Brazil": "Brazil", 
    "Singapore": "Singapore",
    "Russia": "Russia",
    "Indonesia": "Indonesia",
    "Taiwan": "Taiwan",
    "United States": "United States",
    "Vietnam": "Vietnam",
    "Thailand": "Thailand",
    "Middle East": "Middle East",
    "Pakistan": "Pakistan",
    "CIS": "CIS",
    "Bangladesh": "Bangladesh"
}

# Audio languages
AUDIO_LANGUAGES = {
    "English": "English",
    "Urdu": "Urdu", 
    "Hindi": "Hindi"
}

# Request headers
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# Timeouts and delays
REQUEST_TIMEOUT = 30
RETRY_ATTEMPTS = 3
DELAY_BETWEEN_REQUESTS = 1

# Output settings
OUTPUT_DIR = "output"
DEFAULT_OUTPUT_FORMAT = "json"  # json, csv, both
