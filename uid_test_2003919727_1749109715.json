{"uid": "**********", "region": "India", "status": "alert_error", "error": "Alert Text: &#10060; Failed to fetch data:\nAccount API: {\"code\":\"FORBIDDEN\",\"message\":\"Access Forbidden: Invalid Origin. See hlgamingofficial.com/p/api.html\"}\nMessage: unexpected alert open: {Alert text : &#10060; Failed to fetch data:\nAccount API: {\"code\":\"FORBIDDEN\",\"message\":\"Access Forbidden: Invalid Origin. See hlgamingofficial.com/p/api.html\"}}\n  (Session info: chrome=137.0.7151.56)\nStacktrace:\n\tGetHandleVerifier [0x0x1103763+63299]\n\tGetHandleVerifier [0x0x11037a4+63364]\n\t(No symbol) [0x0xf31113]\n\t(No symbol) [0x0xfc0390]\n\t(No symbol) [0x0xf9e376]\n\t(No symbol) [0x0xf6d6e0]\n\t(No symbol) [0x0xf6e544]\n\tGetHandleVerifier [0x0x135e033+2531347]\n\tGetHandleVerifier [0x0x1359332+2511634]\n\tGetHandleVerifier [0x0x1129eda+220858]\n\tGetHandleVerifier [0x0x111a528+156936]\n\tGetHandleVerifier [0x0x1120c5d+183357]\n\tGetHandleVerifier [0x0x110b6c8+95912]\n\tGetHandleVerifier [0x0x110b870+96336]\n\tGetHandleVerifier [0x0x10f664a+9770]\n\tBaseThreadInitThunk [0x0x75dd5d49+25]\n\tRtlInitializeExceptionChain [0x0x76f7d03b+107]\n\tRtlGetAppContainerNamedObjectPath [0x0x76f7cfc1+561]\n", "data": {}, "api_response": null, "timestamp": **********.4887638}