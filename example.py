#!/usr/bin/env python3
"""
Example usage of Free Fire ID Scraper
"""

from scraper import Free<PERSON><PERSON><PERSON><PERSON>raper
from utils import save_to_json, create_output_directory
import json

def example_single_uid():
    """Example: Scrape a single UID"""
    print("=== Example: Single UID Scraping ===")
    
    # Initialize scraper
    scraper = FreeFireScraper(headless=True, use_selenium=True)
    
    try:
        # Example UID (replace with a real one for testing)
        uid = "123456789"
        region = "India"
        
        print(f"Fetching player info for UID: {uid}")
        
        # Get player information
        player_data = scraper.get_player_info(
            uid=uid,
            region=region,
            audio_language="English",
            use_full_tool=False
        )
        
        if player_data:
            print("✅ Successfully retrieved player data!")
            print(f"Player: {player_data.get('nickname', 'Unknown')}")
            print(f"Level: {player_data.get('level', 'Unknown')}")
            print(f"Likes: {player_data.get('likes', 'Unknown')}")
            
            # Save to file
            create_output_directory("output")
            save_to_json(player_data, f"output/example_{uid}.json")
            
        else:
            print("❌ Failed to retrieve player data")
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        scraper.close()

def example_batch_processing():
    """Example: Batch processing multiple UIDs"""
    print("\n=== Example: Batch Processing ===")
    
    # Sample UIDs (replace with real ones for testing)
    uids = ["123456789", "987654321", "555666777"]
    
    scraper = FreeFireScraper(headless=True, use_selenium=True)
    
    try:
        results = []
        
        for uid in uids:
            print(f"Processing UID: {uid}")
            
            player_data = scraper.get_player_info(
                uid=uid,
                region="India",
                audio_language="English"
            )
            
            if player_data:
                results.append(player_data)
                print(f"✅ Success: {player_data.get('nickname', 'Unknown')}")
            else:
                print(f"❌ Failed: {uid}")
        
        print(f"\nProcessed {len(results)} players successfully")
        
        # Save batch results
        if results:
            create_output_directory("output")
            save_to_json(results, "output/batch_results.json")
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        scraper.close()

def example_different_regions():
    """Example: Testing different regions"""
    print("\n=== Example: Different Regions ===")
    
    uid = "123456789"  # Replace with real UID
    regions = ["India", "Pakistan", "Singapore"]
    
    scraper = FreeFireScraper(headless=True, use_selenium=True)
    
    try:
        for region in regions:
            print(f"Testing region: {region}")
            
            player_data = scraper.get_player_info(
                uid=uid,
                region=region,
                audio_language="English"
            )
            
            if player_data:
                print(f"✅ {region}: Found player {player_data.get('nickname', 'Unknown')}")
            else:
                print(f"❌ {region}: No data found")
                
    except Exception as e:
        print(f"Error: {e}")
    finally:
        scraper.close()

def example_requests_only():
    """Example: Using requests only (no Selenium)"""
    print("\n=== Example: Requests Only ===")
    
    scraper = FreeFireScraper(headless=True, use_selenium=False)
    
    try:
        uid = "123456789"  # Replace with real UID
        
        player_data = scraper.get_player_info(
            uid=uid,
            region="India",
            audio_language="English"
        )
        
        if player_data:
            print("✅ Requests-only scraping successful!")
            print(json.dumps(player_data, indent=2))
        else:
            print("❌ Requests-only scraping failed")
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        scraper.close()

if __name__ == "__main__":
    print("Free Fire ID Scraper - Examples")
    print("=" * 40)
    
    # Run examples
    example_single_uid()
    example_batch_processing()
    example_different_regions()
    example_requests_only()
    
    print("\n" + "=" * 40)
    print("Examples completed!")
    print("\nNote: Replace example UIDs with real Free Fire UIDs for actual testing.")
